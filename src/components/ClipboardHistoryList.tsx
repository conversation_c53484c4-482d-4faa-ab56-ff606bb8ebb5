import React, { useMemo } from 'react';
import {
  Box,
  Typography,
  List,
  Chip,
  Alert,
} from '@mui/material';
import { History, Star } from '@mui/icons-material';
import { useAppSelector, useAppDispatch } from '@/store';
import { setSelectedEntry } from '@/store/slices/clipboardSlice';
import { showSnackbar } from '@/store/slices/uiSlice';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';
import ClipboardEntry from './ClipboardEntry';

interface ClipboardHistoryListProps {
  showFavoritesOnly?: boolean;
}

const ClipboardHistoryList: React.FC<ClipboardHistoryListProps> = ({
  showFavoritesOnly = false,
}) => {
  const dispatch = useAppDispatch();
  const { history, favorites, searchQuery, selectedTags } = useAppSelector((state) => state.clipboard);
  const { selectedEntryId } = useKeyboardShortcuts();

  const entries = showFavoritesOnly ? favorites : history;

  // Smart search: Filter entries based on search query with tag support
  const filteredEntries = useMemo(() => {
    let filtered = entries;

    // Parse smart search query
    if (searchQuery.trim()) {
      const query = searchQuery.trim();

      // Check if query contains tag syntax (#tag)
      const tagMatches = query.match(/#(\w+)/g);

      if (tagMatches) {
        // Extract tags from query (remove # prefix)
        const searchTags = tagMatches.map(tag => tag.substring(1).toLowerCase());

        // Filter by tags
        filtered = filtered.filter(entry =>
          searchTags.some(searchTag =>
            entry.tags.some(entryTag => entryTag.toLowerCase().includes(searchTag))
          )
        );

        // Remove tag syntax from query for text search
        const textQuery = query.replace(/#\w+/g, '').trim();

        // If there's remaining text after removing tags, search content
        if (textQuery) {
          const lowerTextQuery = textQuery.toLowerCase();
          filtered = filtered.filter(entry =>
            entry.content.toLowerCase().includes(lowerTextQuery) ||
            entry.name?.toLowerCase().includes(lowerTextQuery) ||
            entry.metadata?.source?.toLowerCase().includes(lowerTextQuery)
          );
        }
      } else {
        // Regular text search (no tag syntax)
        const lowerQuery = query.toLowerCase();
        filtered = filtered.filter(entry =>
          entry.content.toLowerCase().includes(lowerQuery) ||
          entry.name?.toLowerCase().includes(lowerQuery) ||
          entry.tags.some(tag => tag.toLowerCase().includes(lowerQuery)) ||
          entry.metadata?.source?.toLowerCase().includes(lowerQuery)
        );
      }
    }

    // Also filter by selected tags (for backward compatibility)
    if (selectedTags.length > 0) {
      filtered = filtered.filter(entry =>
        selectedTags.some(tag => entry.tags.includes(tag))
      );
    }

    // Sort entries: pinned items first, then by timestamp
    return filtered.sort((a, b) => {
      // Pinned items come first
      if (a.isPinned !== b.isPinned) {
        return a.isPinned ? -1 : 1;
      }
      // Otherwise, sort by timestamp (newest first)
      return b.timestamp - a.timestamp;
    });
  }, [entries, searchQuery, selectedTags]);

  // Remove handleSearchChange since search is now handled in AppLayout

  const handleEntryClick = (entryId: string) => {
    dispatch(setSelectedEntry(entryId));
  };

  const handleEntryCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      dispatch(showSnackbar({ message: 'Copied to clipboard', severity: 'success' }));
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      dispatch(showSnackbar({ message: 'Failed to copy to clipboard', severity: 'error' }));
    }
  };

  // Remove handleTagFilterChange since tag filtering is now handled via smart search

  if (entries.length === 0) {
    return (
      <Box
        display="flex"
        flexDirection="column"
        alignItems="center"
        justifyContent="center"
        height="100%"
        p={4}
      >
        {showFavoritesOnly ? (
          <>
            <Star sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Favorites Yet
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Star clipboard entries to add them to your favorites for quick access.
            </Typography>
          </>
        ) : (
          <>
            <History sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No Clipboard History
            </Typography>
            <Typography variant="body2" color="text.secondary" textAlign="center">
              Copy something to get started. Your clipboard history will appear here.
            </Typography>
          </>
        )}
      </Box>
    );
  }

  return (
    <Box height="100%" display="flex" flexDirection="column">
      {/* Results Info */}
      {(searchQuery || filteredEntries.length !== entries.length) && (
        <Box px={2} py={1}>
          <Box display="flex" alignItems="center" gap={1}>
            <Chip
              label={`${filteredEntries.length} ${filteredEntries.length === 1 ? 'item' : 'items'}`}
              size="small"
              variant="outlined"
            />
            {searchQuery && (
              <Typography variant="caption" color="text.secondary">
                filtered by "{searchQuery}"
              </Typography>
            )}
          </Box>
        </Box>
      )}

      {/* Entry List */}
      <Box flex={1} overflow="auto" px={2}>
        {filteredEntries.length === 0 ? (
          <Alert severity="info" sx={{ mt: 2 }}>
            {searchQuery ? 'No entries match your search criteria.' : 'No entries found.'}
          </Alert>
        ) : (
          <List disablePadding>
            {filteredEntries.map((entry) => (
              <ClipboardEntry
                key={entry.id}
                entry={entry}
                isSelected={entry.id === selectedEntryId}
                onClick={() => handleEntryClick(entry.id)}
                onCopy={() => handleEntryCopy(entry.content)}
              />
            ))}
          </List>
        )}
      </Box>
    </Box>
  );
};

export default ClipboardHistoryList;
